import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get_storage/get_storage.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:xr_helper/xr_helper.dart';

import '../home/<USER>/home_model.dart';


class SettingsScreen extends StatelessWidget {
  final Employee employee;

  const SettingsScreen(
      {super.key, required this.employee});

  @override
  Widget build(BuildContext context) {
    final userName = context.isEnglish
        ? (employee.empName
        ?.split(' ')
                .map((word) => word.capitalizeFirstLetter())
                .join(' ')??
            '');

    final firstLetterOr2LetterIfExist = userName.isNotEmpty
        ? userName.length > 1
            ? userName.substring(0, 2)
            : userName.substring(0, 1)
        : '';

    return Scaffold(
      appBar: AppBar(
        actions: [
          IconButton(
            onPressed: () {
              // materialDialog(
              //   context,
              //   title: context.isEng ? "Logout" : "تسجيل الخروج",
              //   content: context.isEng
              //       ? "Are you sure you want to logout?"
              //       : "هل أنت متأكد أنك تريد تسجيل الخروج؟",
              //   isDelete: true,
              //   actionText: context.isEng ? "Logout" : "تسجيل الخروج",
              //   action: () {
              //     context.read<AuthenticationBloc>().add(const LogoutEvent(''));
              //     GetStorage().erase();
              //     // .add(LogoutEvent(state.account.token!));
              //
              //     if (kDebugMode) {
              //       context.toReplacement(const MainScreen());
              //     } else {
              //       Restart.restartApp();
              //     }
              //   },
              // );
            },
            icon: Icon(
              Icons.logout,
              color: context.isDark ? Colors.white : Colors.black,
            ),
          ),
        ],
        backgroundColor: Colors.transparent,
        title: Padding(
          padding: const EdgeInsets.only(top: AppSpaces.padding4),
          child: Text(
            context.tr.settings,
            style: AppTextStyles.title,
          ),
        ),
        centerTitle: true,
        leading: IconButton(
            onPressed: () => context.back(),
            icon: Icon(
              Icons.arrow_back_ios_new,
              color: context.isDark ? Colors.white : Colors.black,
            )),
      ),
      body: Padding(
        padding: const EdgeInsets.all(AppSpaces.large),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(AppSpaces.medium + 4),
              decoration: BoxDecoration(
                color: context.theme.colorScheme.primary.withOpacity(.2),
                borderRadius: BorderRadius.circular(AppSpaces.medium),
              ),
              child: Text(
                firstLetterOr2LetterIfExist,
                style: context.titleStyle.copyWith(
                  color: context.theme.colorScheme.primary,
                  fontSize: 24,
                ),
              ),
            ),
            context.largeGap,
            Text(
              userName,
              style: context.titleStyle.copyWith(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            context.largeGap,
            Container(
              padding: const EdgeInsets.all(AppSpaces.medium),
              decoration: BoxDecoration(
                color: context.cardColor,
                borderRadius: BorderRadius.circular(AppSpaces.medium),
              ),
              child: Column(
                children: [
                  SettingCard(
                      icon: Icons.color_lens_outlined,
                      title: local.theme,
                      child: DropdownButton<ThemeMode>(
                        borderRadius: BorderRadius.circular(AppSpaces.medium),
                        value: controller!.themeMode,
                        alignment: AlignmentDirectional.centerEnd,
                        underline: const Center(),
                        onChanged: controller!.updateThemeMode,
                        icon: Padding(
                          padding: EdgeInsets.only(
                              bottom: context.isDark ? 0 : AppSpaces.xSmall),
                          child: const Icon(Icons.keyboard_arrow_down),
                        ),
                        items: [
                          DropdownMenuItem(
                            value: ThemeMode.system,
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: AppSpaces.medium),
                              child: Text(local.systemSetting),
                            ),
                          ),
                          DropdownMenuItem(
                            value: ThemeMode.light,
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: AppSpaces.medium),
                              child: Text(local.light),
                            ),
                          ),
                          DropdownMenuItem(
                            value: ThemeMode.dark,
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: AppSpaces.medium),
                              child: Text(local.dark),
                            ),
                          )
                        ],
                      )),
                  SettingCard(
                    icon: Icons.language,
                    title: local.language,
                    child: DropdownButton<Locale>(
                        value: controller!.locale,
                        underline: const Center(),
                        icon: Padding(
                          padding: EdgeInsets.only(
                              bottom: context.isDark ? 0 : AppSpaces.xSmall),
                          child: const Icon(Icons.keyboard_arrow_down),
                        ),
                        borderRadius: BorderRadius.circular(AppSpaces.medium),
                        onChanged: controller!.updateLanguage,
                        items: AppLocalizations.supportedLocales
                            .map((loc) => DropdownMenuItem(
                                  value: loc,
                                  child: Text(loc.languageCode == 'en'
                                      ? 'English'
                                      : 'العربية'),
                                ))
                            .toList()),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
